"""
Base class for live streaming data queries.
Extends BaseQueryService with live-specific functionality and caching strategies.
"""

from datetime import datetime
from typing import Any, Optional

from vups.logger import logger
from vups_server.base.query_base import BaseQueryService
from vups_server.sql.sentence.live_sql import get_danmu_table_name


class LiveQueryBase(BaseQueryService):
    """
    Base class for live streaming data queries.

    Provides live-specific functionality including:
    - Danmu table handling
    - Live-specific validation
    - Room ID validation
    - Specialized error handling for live data
    """

    def __init__(self, cache_ttl: int = 300):
        """
        Initialize LiveQueryBase.

        Args:
            cache_ttl: Default cache TTL in seconds
        """
        super().__init__(cache_ttl)

    def _validate_room_id(self, room_id: Any) -> str:
        """
        Validate and convert room_id to string (live-specific validation).

        Args:
            room_id: Room ID to validate

        Returns:
            Validated room ID as string

        Raises:
            ValueError: If room_id is invalid
        """
        if room_id is None:
            raise ValueError("room_id cannot be None")

        room_id_str = str(room_id).strip()
        if not room_id_str:
            raise ValueError("room_id cannot be empty")

        # Additional live-specific validation could be added here
        # e.g., checking if room_id is numeric, within valid ranges, etc.

        return room_id_str

